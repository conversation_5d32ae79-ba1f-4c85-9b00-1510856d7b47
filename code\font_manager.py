#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一字体管理模块
解决matplotlib中文字体显示问题，避免字体设置冲突
"""

import matplotlib.pyplot as plt
import matplotlib as mpl
import platform
import warnings
from pathlib import Path

# 抑制字体警告
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

class FontManager:
    """字体管理器 - 彻底解决字体问题"""

    def __init__(self):
        self.is_initialized = False
        self.current_font = None
        self.use_english_only = True  # 强制使用英文
        self.fallback_fonts = [
            'DejaVu Sans',
            'Arial',
            'Helvetica',
            'Liberation Sans',
            'sans-serif'
        ]
        
    def initialize_fonts(self):
        """初始化字体设置 - 彻底解决字体问题"""
        try:
            # 完全重置matplotlib参数
            plt.rcParams.update(plt.rcParamsDefault)

            # 强制使用英文，避免所有中文字体问题
            self.use_english_only = True

            # 设置最安全的字体配置
            plt.rcParams['font.family'] = 'sans-serif'
            plt.rcParams['font.sans-serif'] = self.fallback_fonts
            plt.rcParams['axes.unicode_minus'] = False
            plt.rcParams['figure.max_open_warning'] = 0

            # 禁用字体缓存
            plt.rcParams['font.size'] = 10
            plt.rcParams['axes.titlesize'] = 12
            plt.rcParams['axes.labelsize'] = 10
            plt.rcParams['xtick.labelsize'] = 9
            plt.rcParams['ytick.labelsize'] = 9
            plt.rcParams['legend.fontsize'] = 9

            # 设置绘图样式
            self._set_safe_plot_style()

            self.current_font = 'sans-serif'
            self.is_initialized = True

            print("✅ 安全字体模式已启用（仅英文）")

            # 强制重新应用字体设置，防止被其他模块覆盖
            self._force_apply_font_settings()

        except Exception as e:
            print(f"❌ 字体初始化失败: {e}")
            self._emergency_fallback()

    def _force_apply_font_settings(self):
        """强制应用字体设置，防止被其他模块覆盖"""
        try:
            # 多次设置确保生效
            for _ in range(3):
                plt.rcParams['font.family'] = 'sans-serif'
                plt.rcParams['font.sans-serif'] = self.fallback_fonts
                plt.rcParams['axes.unicode_minus'] = False

            # 清除matplotlib的字体缓存
            try:
                import matplotlib.font_manager as fm
                fm._rebuild()
            except:
                pass

        except Exception as e:
            print(f"强制应用字体设置失败: {e}")
    
    # 删除所有中文字体相关代码，简化为英文字体管理
    
    def _set_safe_plot_style(self):
        """设置安全的绘图样式"""
        try:
            # 不使用seaborn，避免额外的字体问题
            plt.style.use('default')

            # 手动设置网格样式
            plt.rcParams['axes.grid'] = True
            plt.rcParams['grid.alpha'] = 0.3
            plt.rcParams['axes.axisbelow'] = True

        except Exception as e:
            print(f"样式设置失败: {e}")
            pass
    
    def _emergency_fallback(self):
        """紧急备用方案 - 最安全的配置"""
        try:
            # 完全重置到默认状态
            plt.rcParams.update(plt.rcParamsDefault)

            # 使用最基本的字体设置
            plt.rcParams['font.family'] = 'sans-serif'
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']
            plt.rcParams['axes.unicode_minus'] = False

            self.current_font = 'sans-serif'
            self.use_english_only = True
            self.is_initialized = True

            print("⚠️ 使用紧急字体配置")

        except Exception:
            # 如果连这个都失败，就什么都不做
            self.use_english_only = True
            self.is_initialized = True
    
    def get_safe_title(self, chinese_text, english_text):
        """获取安全的标题文本 - 强制使用英文避免字体问题"""
        # 为了彻底解决字体问题，始终返回英文
        return english_text

    def get_safe_label(self, chinese_text, english_text):
        """获取安全的标签文本 - 强制使用英文避免字体问题"""
        # 为了彻底解决字体问题，始终返回英文
        return english_text

# 全局字体管理器实例
_font_manager = FontManager()

def initialize_fonts():
    """初始化字体（全局函数）"""
    _font_manager.initialize_fonts()

def get_safe_title(chinese_text, english_text):
    """获取安全的标题文本（全局函数）"""
    if not _font_manager.is_initialized:
        _font_manager.initialize_fonts()
    return _font_manager.get_safe_title(chinese_text, english_text)

def get_safe_label(chinese_text, english_text):
    """获取安全的标签文本（全局函数）"""
    if not _font_manager.is_initialized:
        _font_manager.initialize_fonts()
    return _font_manager.get_safe_label(chinese_text, english_text)

def safe_text_display(chinese_text, english_text):
    """安全的文本显示（别名）"""
    return get_safe_title(chinese_text, english_text)

def ensure_safe_fonts():
    """确保安全字体设置（在每次绘图前调用）"""
    if not _font_manager.is_initialized:
        _font_manager.initialize_fonts()
    else:
        # 重新应用字体设置，防止被其他模块覆盖
        _font_manager._force_apply_font_settings()

# 自动初始化
initialize_fonts()

# 导出常用函数
__all__ = [
    'initialize_fonts',
    'get_safe_title',
    'get_safe_label',
    'safe_text_display',
    'ensure_safe_fonts'
]
