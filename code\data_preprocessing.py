import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.feature_selection import RFECV, SelectKBest, f_classif, mutual_info_classif, VarianceThreshold
from sklearn.ensemble import RandomForestClassifier
from sklearn.tree import DecisionTreeClassifier
from sklearn.linear_model import LogisticRegression
import os
import joblib
from joblib import dump, load
from pathlib import Path
import matplotlib.pyplot as plt
from config import CACHE_PATH, OUTPUT_PATH
from sklearn.metrics import accuracy_score
import re

# 确保缓存目录存在
CACHE_PATH.mkdir(parents=True, exist_ok=True)

def _perform_basic_validation(df):
    """
    执行基本的数据验证
    
    Args:
        df: 数据框
        
    Returns:
        dict: 验证结果
    """
    validation_results = {
        'shape': df.shape,
        'missing_values': {
            'total': df.isnull().sum().sum(),
            'by_column': df.isnull().sum().to_dict(),
            'percentage': (df.isnull().sum() / len(df) * 100).to_dict()
        },
        'duplicates': {
            'count': df.duplicated().sum(),
            'percentage': (df.duplicated().sum() / len(df)) * 100
        },
        'data_types': df.dtypes.to_dict(),
        'numeric_columns': df.select_dtypes(include=[np.number]).columns.tolist(),
        'categorical_columns': df.select_dtypes(include=['object', 'category']).columns.tolist(),
        'memory_usage_mb': df.memory_usage(deep=True).sum() / 1024**2
    }
    
    # 检查异常值（仅对数值列）
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    outliers_info = {}
    
    for col in numeric_cols:
        Q1 = df[col].quantile(0.25)
        Q3 = df[col].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)][col]
        outliers_info[col] = {
            'count': len(outliers),
            'percentage': (len(outliers) / len(df)) * 100,
            'bounds': {'lower': lower_bound, 'upper': upper_bound}
        }
    
    validation_results['outliers'] = outliers_info
    
    return validation_results

def _print_validation_summary(validation_results):
    """
    打印验证结果摘要
    
    Args:
        validation_results: 验证结果字典
    """
    if not validation_results:
        return
    
    print("\n" + "="*50)
    print("数据质量验证摘要")
    print("="*50)
    
    # 基本信息
    print(f"数据集形状: {validation_results['shape']}")
    print(f"内存使用: {validation_results['memory_usage_mb']:.2f} MB")
    
    # 缺失值
    missing = validation_results['missing_values']
    print(f"\n缺失值统计:")
    print(f"  总缺失值: {missing['total']}")
    if missing['total'] > 0:
        print("  各列缺失值:")
        for col, count in missing['by_column'].items():
            if count > 0:
                percentage = missing['percentage'][col]
                print(f"    {col}: {count} ({percentage:.1f}%)")
    else:
        print("  ✓ 无缺失值")
    
    # 重复值
    duplicates = validation_results['duplicates']
    print(f"\n重复行统计:")
    if duplicates['count'] > 0:
        print(f"  重复行数: {duplicates['count']} ({duplicates['percentage']:.1f}%)")
    else:
        print("  ✓ 无重复行")
    
    # 数据类型
    print(f"\n数据类型分布:")
    print(f"  数值列: {len(validation_results['numeric_columns'])}")
    print(f"  分类列: {len(validation_results['categorical_columns'])}")
    
    # 异常值
    outliers = validation_results['outliers']
    total_outliers = sum(info['count'] for info in outliers.values())
    print(f"\n异常值检测 (IQR方法):")
    if total_outliers > 0:
        print(f"  总异常值: {total_outliers}")
        for col, info in outliers.items():
            if info['count'] > 0:
                print(f"    {col}: {info['count']} ({info['percentage']:.1f}%)")
    else:
        print("  ✓ 未检测到明显异常值")
    
    # 数据质量评估
    quality_score = _calculate_quality_score(validation_results)
    print(f"\n数据质量评分: {quality_score:.1f}/100")
    
    # 建议
    recommendations = _generate_recommendations(validation_results)
    if recommendations:
        print(f"\n改进建议:")
        for i, rec in enumerate(recommendations, 1):
            print(f"  {i}. {rec}")
    
    print("="*50)

def _calculate_quality_score(validation_results):
    """
    计算数据质量评分
    
    Args:
        validation_results: 验证结果
        
    Returns:
        float: 质量评分 (0-100)
    """
    score = 100.0
    
    # 缺失值扣分
    missing_total = validation_results['missing_values']['total']
    total_cells = validation_results['shape'][0] * validation_results['shape'][1]
    missing_percentage = (missing_total / total_cells) * 100 if total_cells > 0 else 0
    score -= min(missing_percentage * 2, 30)  # 最多扣30分
    
    # 重复值扣分
    duplicate_percentage = validation_results['duplicates']['percentage']
    score -= min(duplicate_percentage, 20)  # 最多扣20分
    
    # 异常值扣分
    total_outliers = sum(info['count'] for info in validation_results['outliers'].values())
    total_samples = validation_results['shape'][0]
    outlier_percentage = (total_outliers / total_samples) * 100 if total_samples > 0 else 0
    score -= min(outlier_percentage, 20)  # 最多扣20分
    
    return max(score, 0.0)

def _generate_recommendations(validation_results):
    """
    生成数据质量改进建议
    
    Args:
        validation_results: 验证结果
        
    Returns:
        list: 建议列表
    """
    recommendations = []
    
    # 缺失值建议
    missing_total = validation_results['missing_values']['total']
    if missing_total > 0:
        recommendations.append("处理缺失值：考虑删除、填充或插值")
    
    # 重复值建议
    if validation_results['duplicates']['count'] > 0:
        recommendations.append("删除重复行以避免数据偏差")
    
    # 异常值建议
    total_outliers = sum(info['count'] for info in validation_results['outliers'].values())
    if total_outliers > 0:
        recommendations.append("检查并处理异常值：确认是否为数据错误或真实极值")
    
    # 数据类型建议
    if len(validation_results['categorical_columns']) > 0:
        recommendations.append("对分类变量进行编码处理（如独热编码或标签编码）")
    
    return recommendations

def validate_and_load_data(file_path, target_column=None, perform_validation=True):
    """
    验证并加载数据
    
    Args:
        file_path: 数据文件路径
        target_column: 目标列名，如果为None则自动检测
        perform_validation: 是否执行数据验证
        
    Returns:
        tuple: (data, target_column, validation_results)
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")
    
    # 加载数据
    data = pd.read_csv(file_path)
    
    # 执行验证
    validation_results = None
    if perform_validation:
        print(f"正在验证数据: {file_path}")
        validation_results = _perform_basic_validation(data)
        _print_validation_summary(validation_results)
    
    # 检测目标列
    if target_column is None:
        # 改进的目标列检测逻辑
        possible_targets = ['target', 'label', 'class', 'y', 'outcome', 'result']
        for col in possible_targets:
            if col in data.columns:
                target_column = col
                break
        
        if target_column is None:
            # 如果没有找到明显的目标列，选择最后一列
            target_column = data.columns[-1]
            print(f"未找到明显的目标列，使用最后一列作为目标: {target_column}")
        else:
            print(f"自动检测到目标列: {target_column}")
    
    return data, target_column, validation_results

def load_data(file_path):
    """
    加载CSV数据
    
    Args:
        file_path: CSV文件路径
        
    Returns:
        tuple: (X, y, feature_names) 或在出错时 (None, None, None)
    """
    data, target_column, _ = validate_and_load_data(file_path, perform_validation=False)
    
    # 分离特征和目标
    if target_column not in data.columns:
        raise ValueError(f"目标列 '{target_column}' 不存在于数据中")
    
    X = data.drop(columns=[target_column])
    y = data[target_column]
    feature_names = X.columns.tolist()
    
    return X, y, feature_names

def preprocess_data(X, y, test_size=0.2, random_state=42, scaling_method='standard'):
    """
    数据预处理（标准化、归一化）
    
    Args:
        X: 特征数据
        y: 目标变量
        test_size: 测试集比例
        random_state: 随机种子
        scaling_method: 缩放方法 ('standard', 'minmax', 'robust', 'none')
        
    Returns:
        tuple: (X_train, X_test, y_train, y_test, scaler)
    """
    # 对于小数据集，动态调整测试集比例，确保每个类别至少有2个样本
    unique_classes = len(np.unique(y))
    min_samples_per_class = 2
    min_test_samples = unique_classes * min_samples_per_class
    
    # 如果数据集太小，则减小测试集比例
    if len(y) * test_size < min_test_samples:
        adjusted_test_size = min(max(min_test_samples / len(y), 0.1), 0.5)
        print(f"数据集较小，调整测试集比例为: {adjusted_test_size:.2f}")
        test_size = adjusted_test_size
    
    # 检查每个类别的样本数量
    unique_classes, class_counts = np.unique(y, return_counts=True)
    min_class_count = np.min(class_counts)
    
    # 切分训练集和测试集
    if min_class_count < 2:
        print(f"警告：某些类别样本数量不足（最少类别样本数：{min_class_count}），不使用分层抽样")
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state
        )
    else:
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state, stratify=y
        )
    
    # 保存特征名称（如果X是DataFrame）
    feature_names = None
    if hasattr(X, 'columns'):
        feature_names = X.columns.tolist()
    
    # 数据缩放
    scaler = None
    if scaling_method == 'standard':
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
    elif scaling_method == 'minmax':
        scaler = MinMaxScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
    elif scaling_method == 'robust':
        scaler = RobustScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
    elif scaling_method == 'none':
        # 不进行缩放
        X_train_scaled = X_train
        X_test_scaled = X_test
    else:
        print(f"未知的缩放方法: {scaling_method}，使用标准化")
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
    
    # 如果原始数据是DataFrame，则将缩放后的数据转换回DataFrame以保留列名
    if feature_names is not None:
        X_train_scaled = pd.DataFrame(X_train_scaled, columns=feature_names, index=X_train.index if hasattr(X_train, 'index') else None)
        X_test_scaled = pd.DataFrame(X_test_scaled, columns=feature_names, index=X_test.index if hasattr(X_test, 'index') else None)
    
    return X_train_scaled, X_test_scaled, y_train, y_test, scaler

def select_features(X_train, y_train, X_test, feature_names, model_type, method='rfecv', k=10):
    """
    根据模型类型选择合适的特征选择方法
    
    Args:
        X_train: 训练集特征
        y_train: 训练集标签
        X_test: 测试集特征
        feature_names: 特征名称列表
        model_type: 模型类型 ('tree', 'linear', 'ensemble', 'all')
        method: 特征选择方法 ('rfecv', 'kbest', 'variance', 'importance', 'mutual_info')
        k: 选择的特征数量（适用于kbest和importance方法）
        
    Returns:
        tuple: (X_train_selected, X_test_selected, selected_feature_names, selector)
    """
    selector = None
    
    # 将输入数据转换为numpy数组进行处理
    X_train_np = X_train.values if hasattr(X_train, 'values') else X_train
    X_test_np = X_test.values if hasattr(X_test, 'values') else X_test
    
    # 根据模型类型选择基本的估计器
    if model_type == 'tree':
        estimator = DecisionTreeClassifier(random_state=42)
    elif model_type == 'linear':
        estimator = LogisticRegression(random_state=42, max_iter=1000)
    elif model_type == 'ensemble' or model_type == 'all':
        estimator = RandomForestClassifier(n_estimators=100, random_state=42)
    else:
        print(f"未知的模型类型: {model_type}，使用随机森林")
        estimator = RandomForestClassifier(n_estimators=100, random_state=42)
    
    # 特征选择方法
    if method == 'rfecv':
        # 递归特征消除与交叉验证
        print("使用递归特征消除与交叉验证(RFECV)进行特征选择")
        selector = RFECV(
            estimator=estimator,
            step=1,
            cv=StratifiedKFold(5),
            scoring='roc_auc',
            min_features_to_select=5
        )
        X_train_selected = selector.fit_transform(X_train_np, y_train)
        X_test_selected = selector.transform(X_test_np)
        
        # 获取选择的特征
        selected_indices = np.where(selector.support_)[0]
        selected_feature_names = [feature_names[i] for i in selected_indices]
        
        # 绘制特征选择曲线
        plt.figure(figsize=(10, 6))
        plt.title('递归特征消除交叉验证结果')
        plt.xlabel('特征数量')
        plt.ylabel('交叉验证得分')
        plt.plot(range(1, len(selector.grid_scores_) + 1), selector.grid_scores_)
        plt.grid()
        
        # 创建特征选择目录
        feature_dir = OUTPUT_PATH / 'feature_selection'
        feature_dir.mkdir(parents=True, exist_ok=True)
        plt.savefig(feature_dir / f'rfecv_feature_selection_{model_type}.png', dpi=150)
        plt.close()
        
    elif method == 'kbest':
        # 基于统计测试的特征选择
        print(f"使用KBest(f_classif)选择{k}个最佳特征")
        selector = SelectKBest(f_classif, k=min(k, X_train_np.shape[1]))
        X_train_selected = selector.fit_transform(X_train_np, y_train)
        X_test_selected = selector.transform(X_test_np)
        
        # 获取选择的特征
        selected_indices = selector.get_support(indices=True)
        selected_feature_names = [feature_names[i] for i in selected_indices]
        
        # 绘制特征得分
        plt.figure(figsize=(12, 8))
        scores = selector.scores_
        indices = np.argsort(scores)[-k:]
        plt.title('特征重要性得分 (ANOVA F-test)')
        plt.barh(range(len(indices)), scores[indices], align='center')
        plt.yticks(range(len(indices)), [feature_names[i] for i in indices])
        plt.xlabel('F值')
        plt.tight_layout()
        
        # 创建特征选择目录
        feature_dir = OUTPUT_PATH / 'feature_selection'
        feature_dir.mkdir(parents=True, exist_ok=True)
        plt.savefig(feature_dir / f'kbest_feature_selection_{model_type}.png', dpi=150)
        plt.close()
        
    elif method == 'mutual_info':
        # 基于互信息的特征选择
        print(f"使用互信息选择{k}个最佳特征")
        selector = SelectKBest(mutual_info_classif, k=min(k, X_train_np.shape[1]))
        X_train_selected = selector.fit_transform(X_train_np, y_train)
        X_test_selected = selector.transform(X_test_np)
        
        # 获取选择的特征
        selected_indices = selector.get_support(indices=True)
        selected_feature_names = [feature_names[i] for i in selected_indices]
        
    elif method == 'variance':
        # 方差阈值特征选择
        print("使用方差阈值进行特征选择")
        selector = VarianceThreshold(threshold=0.01)
        X_train_selected = selector.fit_transform(X_train_np, y_train)
        X_test_selected = selector.transform(X_test_np)
        
        # 获取选择的特征
        selected_indices = selector.get_support(indices=True)
        selected_feature_names = [feature_names[i] for i in selected_indices]
        
    elif method == 'importance':
        # 基于特征重要性的选择
        print(f"使用特征重要性选择前{k}个特征")
        estimator.fit(X_train_np, y_train)
        
        # 获取特征重要性
        if hasattr(estimator, 'feature_importances_'):
            importances = estimator.feature_importances_
        else:
            print("模型不支持特征重要性，切换为互信息法")
            return select_features(X_train, y_train, X_test, feature_names, model_type, method='mutual_info', k=k)
        
        # 排序并选择前k个特征
        indices = np.argsort(importances)[::-1][:k]
        X_train_selected = X_train_np[:, indices]
        X_test_selected = X_test_np[:, indices]
        selected_feature_names = [feature_names[i] for i in indices]
        
        # 创建一个虚拟的选择器对象，用于保持一致的返回格式
        class DummySelector:
            def __init__(self, indices):
                self.indices = indices
                
            def transform(self, X):
                if hasattr(X, 'values'):
                    return X.values[:, self.indices]
                return X[:, self.indices]
        
        selector = DummySelector(indices)
        
        # 绘制特征重要性
        plt.figure(figsize=(12, 8))
        plt.title('特征重要性')
        plt.barh(range(len(indices)), importances[indices], align='center')
        plt.yticks(range(len(indices)), [feature_names[i] for i in indices])
        plt.xlabel('重要性')
        plt.tight_layout()
        
        # 创建特征选择目录
        feature_dir = OUTPUT_PATH / 'feature_selection'
        feature_dir.mkdir(parents=True, exist_ok=True)
        plt.savefig(feature_dir / f'importance_feature_selection_{model_type}.png', dpi=150)
        plt.close()
        
    else:
        # 不进行特征选择
        print("不进行特征选择")
        X_train_selected = X_train_np
        X_test_selected = X_test_np
        selected_feature_names = feature_names
        
        # 创建一个虚拟的选择器对象
        class IdentitySelector:
            def transform(self, X):
                if hasattr(X, 'values'):
                    return X.values
                return X
        
        selector = IdentitySelector()
    
    print(f"特征选择完成，从{len(feature_names)}个特征中选择了{len(selected_feature_names)}个特征")
    print(f"选择的特征: {selected_feature_names}")
    
    # 将结果转换回DataFrame以保留列名
    if hasattr(X_train, 'columns'):
        import pandas as pd
        X_train_selected = pd.DataFrame(X_train_selected, columns=selected_feature_names, 
                                       index=X_train.index if hasattr(X_train, 'index') else None)
        X_test_selected = pd.DataFrame(X_test_selected, columns=selected_feature_names,
                                      index=X_test.index if hasattr(X_test, 'index') else None)
    
    return X_train_selected, X_test_selected, selected_feature_names, selector

def get_optimal_features_for_model(model_name):
    """
    根据模型名称返回适合的特征选择方法和模型类型
    
    Args:
        model_name: 模型名称
        
    Returns:
        tuple: (model_type, feature_selection_method)
    """
    # 树型模型通常适合使用特征重要性或RFECV
    if model_name in ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost']:
        return 'tree', 'importance'
    
    # 线性模型适合使用RFECV或基于统计的方法
    elif model_name in ['Logistic', 'SVM']:
        return 'linear', 'rfecv'
    
    # KNN和朴素贝叶斯适合方差筛选或互信息
    elif model_name in ['KNN', 'NaiveBayes']:
        return 'all', 'mutual_info'
    
    # 神经网络模型适合使用多种方法
    elif model_name == 'NeuralNet':
        return 'all', 'kbest'
    
    # 默认使用RFECV
    else:
        return 'all', 'rfecv'

def clean_col_names(df):
    """
    清理DataFrame的列名：去除特殊字符，转为小写。
    """
    cols = df.columns
    new_cols = []
    for col in cols:
        new_col = re.sub(r'[^A-Za-z0-9_]+', '', col)
        new_col = new_col.lower()
        new_cols.append(new_col)
    df.columns = new_cols
    return df

def load_and_clean_data(data_path, target_col_name=None):
    """
    只负责加载数据、清理列名，并找到目标列。
    返回一个干净的、完整的DataFrame和目标列的名称。
    """
    try:
        df = pd.read_csv(data_path, encoding='utf-8')
    except UnicodeDecodeError:
        df = pd.read_csv(data_path, encoding='gbk')

    df = clean_col_names(df)

    if target_col_name:
        target_col_name = re.sub(r'[^A-Za-z0-9_]+', '', target_col_name).lower()
    else:
        possible_targets = ['label', 'target', 'class', 'y']
        for col in possible_targets:
            if col in df.columns:
                target_col_name = col
                print(f"自动检测到目标列: {target_col_name}")
                break
    
    if not target_col_name or target_col_name not in df.columns:
        raise ValueError(f"无法找到目标列。请检查数据或指定 'target_col_name'。")

    return df, target_col_name

# 添加全局EnsembleFeatureSelector类，解决pickle问题
class EnsembleFeatureSelector:
    """
    用于模型融合特征选择的选择器类
    """
    def __init__(self, selected_indices, feature_names=None):
        self.selected_indices = selected_indices
        self.feature_names = feature_names
        
    def transform(self, X):
        if hasattr(X, 'values'):
            return X.values[:, self.selected_indices]
        return X[:, self.selected_indices]
    
    def get_support(self, indices=False):
        if self.feature_names is None:
            # 如果没有特征名称，假设特征数量等于最大索引+1
            n_features = max(self.selected_indices) + 1 if len(self.selected_indices) > 0 else 0
        else:
            n_features = len(self.feature_names)
            
        mask = np.zeros(n_features, dtype=bool)
        mask[self.selected_indices] = True
        return self.selected_indices if indices else mask

def feature_selection_for_ensemble(X_train, y_train, X_test, base_models, feature_names=None, method='combined', k=None):
    """
    为模型融合阶段进行特征选择
    
    Args:
        X_train: 训练集特征
        y_train: 训练集标签
        X_test: 测试集特征
        base_models: 基础模型字典 {model_name: model}
        feature_names: 特征名称列表
        method: 特征选择方法 ('combined', 'union', 'intersection', 'weighted', 'meta_model')
        k: 选择的特征数量，如果为None则自动确定
        
    Returns:
        tuple: (X_train_selected, X_test_selected, selected_feature_names, selector)
    """
    print(f"为模型融合进行特征选择，方法: {method}")
    
    # 将输入数据转换为numpy数组进行处理
    X_train_np = X_train.values if hasattr(X_train, 'values') else X_train
    X_test_np = X_test.values if hasattr(X_test, 'values') else X_test
    
    # 如果没有提供特征名称，创建默认名称
    if feature_names is None:
        feature_names = [f'feature_{i}' for i in range(X_train_np.shape[1])]
    
    # 自动确定k值
    if k is None:
        k = max(int(X_train_np.shape[1] * 0.5), 10)  # 默认选择50%的特征，但至少10个
    
    # 存储每个模型的特征重要性
    feature_importances = {}
    
    # 获取每个基础模型的特征重要性
    for model_name, model in base_models.items():
        if hasattr(model, 'feature_importances_'):
            # 树模型通常有feature_importances_属性
            feature_importances[model_name] = model.feature_importances_
        elif hasattr(model, 'coef_'):
            # 线性模型通常有coef_属性
            feature_importances[model_name] = np.abs(model.coef_[0]) if model.coef_.ndim > 1 else np.abs(model.coef_)
        else:
            # 对于其他模型，使用排列重要性
            try:
                from sklearn.inspection import permutation_importance
                # 修复：使用y_train而不是y_test
                result = permutation_importance(model, X_train_np, y_train, n_repeats=10, random_state=42)
                feature_importances[model_name] = result.importances_mean
            except Exception as e:
                print(f"无法为模型 {model_name} 计算特征重要性: {e}")
    
    # 根据不同方法选择特征
    if method == 'combined':
        # 合并所有模型的特征重要性（取平均值）
        combined_importance = np.zeros(len(feature_names))
        for imp in feature_importances.values():
            if len(imp) == len(combined_importance):
                combined_importance += imp
        
        if len(feature_importances) > 0:
            combined_importance /= len(feature_importances)
            
        # 选择top-k特征
        selected_indices = np.argsort(combined_importance)[-k:]
        
    elif method == 'union':
        # 每个模型选择top-k/n特征，然后取并集
        n_models = len(feature_importances)
        if n_models == 0:
            return X_train, X_test, feature_names, None
            
        k_per_model = max(k // n_models, 5)  # 每个模型至少选择5个特征
        all_selected = set()
        
        for imp in feature_importances.values():
            model_selected = set(np.argsort(imp)[-k_per_model:])
            all_selected.update(model_selected)
        
        selected_indices = np.array(list(all_selected))
        
    elif method == 'intersection':
        # 每个模型选择top-k特征，然后取交集
        if not feature_importances:
            return X_train, X_test, feature_names, None
            
        k_per_model = min(k * 2, X_train_np.shape[1] // 2)  # 每个模型选择较多特征以确保有交集
        model_selections = []
        
        for imp in feature_importances.values():
            model_selected = set(np.argsort(imp)[-k_per_model:])
            model_selections.append(model_selected)
        
        # 取交集
        if model_selections:
            common_features = set.intersection(*model_selections)
            # 如果交集太小，回退到并集方法
            if len(common_features) < 5:
                print("特征交集太小，回退到并集方法")
                return feature_selection_for_ensemble(X_train, y_train, X_test, base_models, 
                                                    feature_names, method='union', k=k)
            selected_indices = np.array(list(common_features))
        else:
            selected_indices = np.array([])
        
    elif method == 'weighted':
        # 基于每个模型的性能加权平均特征重要性
        model_weights = {}
        combined_importance = np.zeros(len(feature_names))
        
        # 计算每个模型的权重（基于在训练集上的性能，而不是测试集）
        for model_name, model in base_models.items():
            try:
                # 修复：使用训练集评估模型性能，而不是测试集
                y_pred = model.predict(X_train_np)
                accuracy = accuracy_score(y_train, y_pred)
                model_weights[model_name] = accuracy
            except Exception as e:
                print(f"计算模型 {model_name} 的权重时出错: {e}")
                model_weights[model_name] = 0.5  # 默认权重
        
        # 归一化权重
        total_weight = sum(model_weights.values())
        if total_weight > 0:
            for model_name in model_weights:
                model_weights[model_name] /= total_weight
        
        # 加权平均特征重要性
        for model_name, imp in feature_importances.items():
            if len(imp) == len(combined_importance):
                combined_importance += imp * model_weights.get(model_name, 0)
        
        # 选择top-k特征
        selected_indices = np.argsort(combined_importance)[-k:]
        
    elif method == 'meta_model':
        # 使用元模型进行特征选择
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.feature_selection import SelectFromModel
        
        # 创建元模型
        meta_model = RandomForestClassifier(n_estimators=100, random_state=42)
        selector = SelectFromModel(meta_model, max_features=k, threshold=-np.inf)
        
        # 拟合选择器
        selector.fit(X_train_np, y_train)
        
        # 获取选择的特征索引
        selected_indices = np.where(selector.get_support())[0]
    
    else:
        print(f"未知的特征选择方法: {method}，不进行特征选择")
        return X_train, X_test, feature_names, None
    
    # 检查是否有选择的特征
    if len(selected_indices) == 0:
        print("没有特征被选择，返回原始数据")
        return X_train, X_test, feature_names, None
    
    # 选择特征
    X_train_selected = X_train_np[:, selected_indices]
    X_test_selected = X_test_np[:, selected_indices]
    
    # 获取选择的特征名称
    selected_feature_names = [feature_names[i] for i in selected_indices]
    
    print(f"特征选择完成，从{len(feature_names)}个特征中选择了{len(selected_feature_names)}个特征")
    print(f"选择的特征: {selected_feature_names}")
    
    # 创建一个选择器对象用于后续转换 - 使用全局定义的EnsembleFeatureSelector类
    selector = EnsembleFeatureSelector(selected_indices, feature_names)
    
    # 将结果转换回DataFrame以保留列名
    if hasattr(X_train, 'columns'):
        import pandas as pd
        X_train_selected = pd.DataFrame(X_train_selected, columns=selected_feature_names, 
                                       index=X_train.index if hasattr(X_train, 'index') else None)
        X_test_selected = pd.DataFrame(X_test_selected, columns=selected_feature_names,
                                      index=X_test.index if hasattr(X_test, 'index') else None)
    
    return X_train_selected, X_test_selected, selected_feature_names, selector

def load_and_preprocess_data(data_path, model_name=None, test_size=0.2, random_state=42, scaling_method='standard'):
    """
    兼容主程序的老接口：加载数据、清理列名、自动检测目标列，并完成train/test分割。
    
    Args:
        data_path: 数据文件路径
        model_name: 可选，模型名称，用于特定模型的预处理
        test_size: 测试集比例
        random_state: 随机种子
        scaling_method: 特征缩放方法，'standard'或'minmax'
        
    Returns:
        X_train, X_test, y_train, y_test: 分割后的训练集和测试集
    """
    import pandas as pd
    from sklearn.model_selection import train_test_split
    from sklearn.preprocessing import StandardScaler, MinMaxScaler

    df, target_col = load_and_clean_data(data_path, target_col_name=None)
    X = df.drop(columns=[target_col])
    y = df[target_col]
    
    # 打印数据加载信息
    print(f"成功加载 {model_name if model_name else ''} 数据，训练集大小: {X.shape}")

    # 分割数据集（小样本安全分层：确保每类至少2个样本，否则降级为非分层，并适当调整test_size）
    unique_classes, class_counts = np.unique(y, return_counts=True)
    min_class_count = np.min(class_counts)
    # 如果测试集中每类不足2个样本，则增大或减小test_size到可行范围
    min_test_per_class = 2
    min_total_test = len(unique_classes) * min_test_per_class
    if len(y) * test_size < min_total_test:
        adjusted_test_size = min(max(min_total_test / len(y), 0.1), 0.5)
        print(f"数据集较小，调整测试集比例为: {adjusted_test_size:.2f}")
        test_size = adjusted_test_size
    if min_class_count < 2:
        print(f"警告：某些类别样本数量不足（最少类别样本数：{min_class_count}），不使用分层抽样")
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state
        )
    else:
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state, stratify=y
        )

    # 标准化
    if scaling_method == 'standard':
        scaler = StandardScaler()
    elif scaling_method == 'minmax':
        scaler = MinMaxScaler()
    else:
        scaler = None

    if scaler is not None:
        X_train = pd.DataFrame(scaler.fit_transform(X_train), columns=X_train.columns)
        X_test = pd.DataFrame(scaler.transform(X_test), columns=X_test.columns)

    return X_train, X_test, y_train, y_test

class DataPreprocessor:
    """
    统一的数据预处理器，消除重复的数据处理逻辑
    """

    def __init__(self, test_size=0.2, random_state=42, scaling_method='standard'):
        """
        初始化数据预处理器

        Args:
            test_size: 测试集比例
            random_state: 随机种子
            scaling_method: 特征缩放方法
        """
        self.test_size = test_size
        self.random_state = random_state
        self.scaling_method = scaling_method
        self.scaler = None

    def load_and_preprocess(self, data_path, model_name=None, target_col_name=None):
        """
        加载和预处理数据的统一接口

        Args:
            data_path: 数据文件路径
            model_name: 模型名称（用于日志）
            target_col_name: 目标列名

        Returns:
            X_train, X_test, y_train, y_test: 分割后的训练集和测试集
        """
        import pandas as pd
        from sklearn.model_selection import train_test_split
        from sklearn.preprocessing import StandardScaler, MinMaxScaler

        try:
            from logger import get_logger
            logger = get_logger(__name__)
        except ImportError:
            import logging
            logger = logging.getLogger(__name__)

        # 加载和清理数据
        df, target_col = load_and_clean_data(data_path, target_col_name)
        X = df.drop(columns=[target_col])
        y = df[target_col]

        # 打印数据加载信息
        model_info = f" {model_name}" if model_name else ""
        logger.info(f"成功加载{model_info} 数据，特征维度: {X.shape}")

        # 分割数据集（小样本安全分层）
        unique_classes, class_counts = np.unique(y, return_counts=True)
        min_class_count = np.min(class_counts)
        min_test_per_class = 2
        min_total_test = len(unique_classes) * min_test_per_class
        test_size = self.test_size
        if len(y) * test_size < min_total_test:
            adjusted_test_size = min(max(min_total_test / len(y), 0.1), 0.5)
            logger.info(f"数据集较小，调整测试集比例为: {adjusted_test_size:.2f}")
            test_size = adjusted_test_size
        if min_class_count < 2:
            logger.warning(f"某些类别样本数量不足（最少类别样本数：{min_class_count}），不使用分层抽样")
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=test_size, random_state=self.random_state
            )
        else:
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=test_size, random_state=self.random_state, stratify=y
            )

        # 特征缩放
        if self.scaling_method == 'standard':
            self.scaler = StandardScaler()
        elif self.scaling_method == 'minmax':
            self.scaler = MinMaxScaler()
        else:
            self.scaler = None

        if self.scaler:
            X_train = pd.DataFrame(self.scaler.fit_transform(X_train), columns=X_train.columns)
            X_test = pd.DataFrame(self.scaler.transform(X_test), columns=X_test.columns)
            logger.info(f"应用了 {self.scaling_method} 特征缩放")

        return X_train, X_test, y_train, y_test


# 全局数据预处理器实例
data_preprocessor = DataPreprocessor()

if __name__ == "__main__":
    # 测试数据预处理和特征选择功能
    try:
        preprocessor = DataPreprocessor()
        X_train, X_test, y_train, y_test = preprocessor.load_and_preprocess('path_to_your_data.csv')
        print("数据预处理成功！")
        print(f"训练集形状: {X_train.shape}")
        print(f"测试集形状: {X_test.shape}")
    except Exception as e:
        print(f"测试失败: {e}")
        print("请提供有效的数据文件路径进行测试")


