#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import argparse
import sys
import os
from pathlib import Path
import json

# 动态添加当前目录到 Python 路径，确保模块可以被找到
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 尝试导入配置模块
try:
    from config import (
        OUTPUT_PATH, CACHE_PATH, MODEL_NAMES, DATA_PATH, 
        initialize_matplotlib, GPU_CONFIG
    )
    plt = initialize_matplotlib()
except ImportError:
    # 从config导入路径配置
    from config import OUTPUT_PATH, CACHE_PATH, DATA_PATH
    OUTPUT_PATH.mkdir(parents=True, exist_ok=True)
    CACHE_PATH.mkdir(parents=True, exist_ok=True)
    DATA_PATH.mkdir(parents=True, exist_ok=True)
    
    # 模型列表
    MODEL_NAMES = ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost',
                  'Logistic', 'SVM', 'KNN', 'NaiveBayes', 'NeuralNet']

# 尝试导入日志模块
try:
    from logger import get_default_logger
    logger = get_default_logger("main")
except ImportError:
    import logging
    logger = logging.getLogger("main")
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logger.addHandler(handler)

from data_preprocessing import load_and_preprocess_data
from model_training import (train_decision_tree, train_random_forest, train_xgboost, train_lightgbm, train_catboost,
                             train_logistic, train_svm, train_knn, train_naive_bayes, train_neural_net)
from hyperparameter_tuning import tune_model
from plot_single_model import plot_model_visualizations
from plot_comparison import plot_model_comparison
from model_performance_report import generate_comprehensive_report
from multi_data_ensemble import run_multi_data_ensemble_pipeline
from external_validation import run_external_validation
from model_ensemble import run_ensemble_pipeline
from plot_ensemble import visualize_ensemble_results

# 确保输出目录存在
def ensure_output_dirs():
    """确保输出目录结构存在"""
    # 主输出目录
    OUTPUT_PATH.mkdir(parents=True, exist_ok=True)
    
    # 模型子目录
    for model_name in MODEL_NAMES:
        model_dir = OUTPUT_PATH / model_name
        model_dir.mkdir(exist_ok=True)
    
    # 组合模型目录
    combined_dir = OUTPUT_PATH / 'combined'
    combined_dir.mkdir(exist_ok=True)
    
    logger.info(f"已确保输出目录结构存在: {OUTPUT_PATH}")

if __name__ == "__main__":
    # 创建参数解析器
    parser = argparse.ArgumentParser(description="Train, cache, and visualize models")
    parser.add_argument("--model", type=str, 
                        required=True, help="Select the model(s) to train and optimize. Use 'All' for all models or specify multiple models separated by commas (e.g. 'RandomForest,XGBoost,LightGBM')")
    parser.add_argument("--mode", type=str, choices=["train", "plot", "compare", "tune", "report", "ensemble", "multi_data_ensemble", "external_validation"], default="train",
                         help="Mode: 'train' to train and cache, 'plot' to visualize single model from cache, 'compare' to compare all models, 'tune' to tune hyperparameters, 'report' to generate performance report, 'ensemble' to run ensemble learning, 'multi_data_ensemble' to run multi-data ensemble learning, 'external_validation' to validate models on external dataset")
    parser.add_argument("--data", type=str, default=None,
                        help="Path to the data file (CSV format)")
    parser.add_argument("--n_trials", type=int, default=50,
                        help="Number of trials for hyperparameter tuning")
    
    # 特征筛选参数
    parser.add_argument("--feature_selection", type=str, choices=["True", "False"], default="True",
                        help="是否进行特征筛选（用于ensemble和multi_data_ensemble模式）")
    parser.add_argument("--feature_selection_method", type=str, 
                        choices=["combined", "union", "intersection", "weighted", "meta_model"], 
                        default="weighted",
                        help="特征筛选方法（用于ensemble和multi_data_ensemble模式）")
    parser.add_argument("--feature_selection_k", type=int, default=None,
                        help="选择的特征数量，如果为None则自动确定（用于ensemble和multi_data_ensemble模式）")
    
    # 多数据源集成学习参数
    parser.add_argument('--model_data_config', type=str, 
                        help='模型与数据源映射配置文件路径（JSON格式）')
    parser.add_argument('--multi_ensemble_methods', type=str, nargs='+',
                        choices=['voting', 'stacking', 'weighted'],
                        default=['voting', 'stacking', 'weighted'],
                        help='多数据源集成方法列表')
    parser.add_argument('--ensemble_data_strategies', type=str, nargs='+',
                        choices=['unified', 'combined', 'original'],
                        default=['unified', 'combined'],
                        help='集成数据策略列表')
    parser.add_argument('--target_data_path', type=str,
                        help='目标数据路径（用于unified策略）')
    
    # 外部验证参数
    parser.add_argument('--external_data', type=str,
                        help='外部验证数据集路径（CSV格式）')
    
    args = parser.parse_args()
    
    # 确保输出目录结构存在
    ensure_output_dirs()
    
    # 设置数据文件路径
    if args.data:
        data_file = args.data
    elif args.mode == "report":
        # 在report模式下，尝试从缓存中获取最后使用的数据文件路径
        cache_data_file = CACHE_PATH / 'last_used_data_file.txt'
        if cache_data_file.exists():
            with open(cache_data_file, 'r', encoding='utf-8') as f:
                data_file = f.read().strip()
            logger.info(f"从缓存中读取数据文件路径: {data_file}")
        else:
            data_file = str(DATA_PATH / 'varnode13.csv')
            logger.warning(f"未找到缓存的数据文件路径，使用默认路径: {data_file}")
    else:
        data_file = str(DATA_PATH / 'varnode13.csv')

    # 处理模型参数，支持逗号分隔的多个模型
    if args.model == "All":
        selected_models = MODEL_NAMES
    else:
        # 分割逗号分隔的模型名称列表
        selected_models = [model.strip() for model in args.model.split(',')]
        # 检查所有模型名称是否有效
        invalid_models = [model for model in selected_models if model not in MODEL_NAMES]
        if invalid_models:
            logger.error(f"错误: 以下模型名称无效: {', '.join(invalid_models)}")
            logger.info(f"支持的模型: {', '.join(MODEL_NAMES)}")
            exit(1)

    if args.mode == "train":
        # 保存当前使用的数据文件路径到缓存
        cache_data_file = CACHE_PATH / 'last_used_data_file.txt'
        with open(cache_data_file, 'w', encoding='utf-8') as f:
            f.write(data_file)
        logger.info(f"已保存数据文件路径到缓存: {data_file}")
        
        # 加载和预处理数据
        X_train, X_test, y_train, y_test = load_and_preprocess_data(data_file)

        # 检查数据是否加载成功
        if X_train is None or X_test is None or y_train is None or y_test is None:
            logger.error("数据加载失败，程序退出。")
            exit(1)

        # 获取模型训练函数
        from config import get_model_functions
        models = get_model_functions()

        if args.model == "All":
            logger.info("开始训练和优化所有模型")
            for model_name, train_func in models.items():
                logger.info(f"训练和优化 {model_name}")
                best_params, best_score = tune_model(
                    model_name, args.n_trials, X_train, y_train,
                    early_stopping_rounds=True, patience=10, min_improvement=0.001,
                    strategy='TPE', n_jobs=1
                )
                logger.info(f"使用最佳参数重新训练 {model_name}, 最佳分数: {best_score:.4f}")
                train_func(X_train, y_train, X_test, y_test, params=best_params)
        else:
            for model_name in selected_models:
                logger.info(f"训练和优化 {model_name}")
                train_func = models[model_name]
                best_params, best_score = tune_model(
                    model_name, args.n_trials, X_train, y_train,
                    early_stopping_rounds=True, patience=10, min_improvement=0.001,
                    strategy='TPE', n_jobs=1
                )
                logger.info(f"使用最佳参数重新训练 {model_name}, 最佳分数: {best_score:.4f}")
                train_func(X_train, y_train, X_test, y_test, params=best_params)

    elif args.mode == "plot":
        if args.model == "All":
            logger.info("可视化所有模型的缓存结果")
            for model_name in MODEL_NAMES:
                logger.info(f"正在绘制 {model_name} 的可视化图表...")
                plot_model_visualizations(model_name)
        else:
            for model_name in selected_models:
                logger.info(f"可视化 {model_name} 的缓存结果")
                plot_model_visualizations(model_name)

    elif args.mode == "compare":
        logger.info("比较所有模型的性能")
        # 确保combined目录存在
        combined_dir = OUTPUT_PATH / 'combined'
        combined_dir.mkdir(exist_ok=True)
        plot_model_comparison()
        logger.info(f"比较图表已保存到: {combined_dir}")
    
    elif args.mode == "tune":
        # 超参数调优模式
        if args.model == "All":
            logger.error("超参数调优模式不支持'All'选项，请指定具体模型")
            exit(1)
        
        if len(selected_models) > 1:
            logger.error("超参数调优模式一次只支持一个模型")
            exit(1)
            
        model_name = selected_models[0]
        logger.info(f"开始为 {model_name} 进行超参数调优")
        
        # 加载和预处理数据
        X_train, X_test, y_train, y_test = load_and_preprocess_data(data_file, model_name=model_name)
        
        # 检查数据是否加载成功
        if X_train is None or y_train is None:
            logger.error("数据加载失败，程序退出。")
            exit(1)
            
        # 进行超参数调优
        best_params, best_score = tune_model(
            model_name, args.n_trials, X_train, y_train,
            early_stopping_rounds=True, patience=10, min_improvement=0.001,
            strategy='TPE', n_jobs=1
        )
        logger.info(f"{model_name} 最佳参数: {best_params}")
        logger.info(f"{model_name} 最佳得分: {best_score:.4f}")
    
    elif args.mode == "report":
        logger.info("生成综合性能报告")
        generate_comprehensive_report(selected_models)
        logger.info(f"报告已生成并保存至: {OUTPUT_PATH / 'reports'}")
    
    elif args.mode == "ensemble":
        logger.info("开始单数据源集成学习")

        # 加载和预处理数据
        X_train, X_test, y_train, y_test = load_and_preprocess_data(data_file)

        # 检查数据是否加载成功
        if X_train is None or X_test is None or y_train is None or y_test is None:
            logger.error("数据加载失败，程序退出。")
            exit(1)

        logger.info(f"使用数据源 '{data_file}' 为 {len(selected_models)} 个模型运行集成学习...")
        logger.info(f"选择的模型: {', '.join(selected_models)}")

        # 运行单数据源集成学习
        ensemble_results = run_ensemble_pipeline(
            X_train=X_train,
            y_train=y_train,
            X_test=X_test,
            y_test=y_test,
            model_names=selected_models,
            ensemble_methods=['voting', 'bagging', 'boosting', 'stacking'],
            save_results=True,
            output_dir=OUTPUT_PATH / 'ensemble',
            enable_shap=True
        )

        if ensemble_results:
            logger.info("集成学习完成，开始生成可视化...")
            # 生成可视化
            visualize_ensemble_results(
                ensemble_results=ensemble_results,
                X_train=X_train,
                y_train=y_train,
                output_dir=OUTPUT_PATH / 'ensemble' / 'visualizations'
            )
            logger.info("集成学习可视化完成")
        else:
            logger.error("集成学习失败")
    
    elif args.mode == "multi_data_ensemble":
        if not args.model_data_config:
            logger.error("多数据源集成学习需要提供 --model_data_config 参数")
            exit(1)
            
        logger.info("开始多数据源集成学习")
        
        # 读取JSON配置文件
        try:
            with open(args.model_data_config, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                model_data_mapping = config_data.get('model_data_mapping', {})
                
                if not model_data_mapping:
                    logger.error(f"配置文件 {args.model_data_config} 中未找到有效的model_data_mapping")
                    exit(1)
                    
                logger.info(f"成功加载配置文件，包含 {len(model_data_mapping)} 个模型-数据源映射")
                
        except Exception as e:
            logger.error(f"读取配置文件 {args.model_data_config} 失败: {e}")
            exit(1)
        
        # 解析特征筛选参数
        feature_selection = args.feature_selection.lower() == "true"
        feature_selection_method = args.feature_selection_method
        feature_selection_k = args.feature_selection_k
        
        logger.info(f"特征筛选设置: 启用={feature_selection}, 方法={feature_selection_method}, 特征数量={feature_selection_k if feature_selection_k else '自动'}")
        
        # 运行多数据源集成学习
        run_multi_data_ensemble_pipeline(
            model_data_mapping=model_data_mapping,
            ensemble_methods=args.multi_ensemble_methods,
            ensemble_data_strategies=args.ensemble_data_strategies,
            target_data_path=args.target_data_path,
            feature_names=None,  # 可选：提供特征名称列表
            enable_shap=True,    # 启用SHAP分析
            feature_selection=feature_selection,
            feature_selection_method=feature_selection_method,
            k=feature_selection_k
        )
        logger.info("多数据源集成学习完成")
        
    elif args.mode == "external_validation":
        # 外部验证模式
        if not args.data and not args.external_data:
            logger.error("外部验证模式需要提供 --data 或 --external_data 参数指定外部数据集")
            exit(1)
            
        external_data_file = args.external_data if args.external_data else args.data
        
        # 添加对--report参数的支持
        parser.add_argument("--report", action="store_true", 
                          help="是否只生成报告而不运行验证")
        
        # 重新解析参数以获取新添加的--report参数
        args, _ = parser.parse_known_args()
        
        # 如果只需要生成报告
        if args.report:
            logger.info(f"生成外部验证综合报告")
            from external_validation import generate_external_validation_report_for_all_models
            generate_external_validation_report_for_all_models(selected_models, external_data_file)
            logger.info(f"外部验证综合报告已生成")
            logger.info(f"报告已保存至: {OUTPUT_PATH / 'external_validation' / 'reports'}")
        else:
            # 运行外部验证
            logger.info(f"开始在外部数据集上验证模型: {external_data_file}")
            success_count = 0
            
            for model_name in selected_models:
                logger.info(f"验证模型: {model_name}")
                if run_external_validation(model_name, external_data_file):
                    success_count += 1
                    logger.info(f"模型 {model_name} 外部验证完成")
                else:
                    logger.error(f"模型 {model_name} 外部验证失败")
                    
            logger.info(f"外部验证完成，成功验证 {success_count}/{len(selected_models)} 个模型")
            logger.info(f"验证结果已保存至: {OUTPUT_PATH / 'external_validation'}")
            
            # 如果验证了多个模型，自动生成综合报告
            if len(selected_models) > 1:
                logger.info("生成外部验证综合报告")
                from external_validation import generate_external_validation_report_for_all_models
                generate_external_validation_report_for_all_models(selected_models, external_data_file)
                logger.info(f"外部验证综合报告已生成")
                logger.info(f"报告已保存至: {OUTPUT_PATH / 'external_validation' / 'reports'}")
    
    else:
        logger.error(f"未知的模式: {args.mode}")
        exit(1)
