{"strategy": "interpretability", "best_model": "RandomForest", "best_score": 0.8752237851662404, "top_models": [["RandomForest", 0.8752237851662404], ["Logistic", 0.8669277493606138], ["CatBoost", 0.8543158567774937], ["DecisionTree", 0.8540281329923274], ["XGBoost", 0.841645838177168]], "all_scores": {"DecisionTree": 0.8540281329923274, "RandomForest": 0.8752237851662404, "XGBoost": 0.841645838177168, "LightGBM": 0.8375537665659148, "CatBoost": 0.8543158567774937, "Logistic": 0.8669277493606138, "SVM": 0.7041432225063939, "KNN": 0.8097404673331783, "NaiveBayes": 0.8249025451695404, "NeuralNet": 0.740111311322948}, "detailed_metrics": {"DecisionTree": {"accuracy": 0.8, "precision": 0.8, "recall": 0.7058823529411765, "f1_score": 0.75, "mcc": 0.5875955600576713, "kappa": 0.5844155844155844, "auc_roc": 0.8951406649616369, "auc_pr": 0.794830659536542, "specificity": 0.8695652173913043, "sensitivity": 0.7058823529411765, "balanced_accuracy": 0.7877237851662404, "interpretability_score": 1.0}, "RandomForest": {"accuracy": 0.9, "precision": 0.9333333333333333, "recall": 0.8235294117647058, "f1_score": 0.875, "mcc": 0.7965184258559546, "kappa": 0.7922077922077921, "auc_roc": 0.9386189258312021, "auc_pr": 0.9390970495829831, "specificity": 0.9565217391304348, "sensitivity": 0.8235294117647058, "balanced_accuracy": 0.8900255754475703, "interpretability_score": 0.8}, "XGBoost": {"accuracy": 0.875, "precision": 0.875, "recall": 0.8235294117647058, "f1_score": 0.8484848484848485, "mcc": 0.7432561251138006, "kappa": 0.7422680412371134, "auc_roc": 0.9667519181585678, "auc_pr": 0.9556245561435873, "specificity": 0.9130434782608695, "sensitivity": 0.8235294117647058, "balanced_accuracy": 0.8682864450127876, "interpretability_score": 0.7}, "LightGBM": {"accuracy": 0.875, "precision": 0.875, "recall": 0.8235294117647058, "f1_score": 0.8484848484848485, "mcc": 0.7432561251138006, "kappa": 0.7422680412371134, "auc_roc": 0.9462915601023019, "auc_pr": 0.9452631578947368, "specificity": 0.9130434782608695, "sensitivity": 0.8235294117647058, "balanced_accuracy": 0.8682864450127876, "interpretability_score": 0.7}, "CatBoost": {"accuracy": 0.9, "precision": 0.9333333333333333, "recall": 0.8235294117647058, "f1_score": 0.875, "mcc": 0.7965184258559546, "kappa": 0.7922077922077921, "auc_roc": 0.959079283887468, "auc_pr": 0.9570278637770897, "specificity": 0.9565217391304348, "sensitivity": 0.8235294117647058, "balanced_accuracy": 0.8900255754475703, "interpretability_score": 0.7}, "Logistic": {"accuracy": 0.85, "precision": 0.8666666666666667, "recall": 0.7647058823529411, "f1_score": 0.8125, "mcc": 0.6920569929568129, "kappa": 0.6883116883116883, "auc_roc": 0.928388746803069, "auc_pr": 0.9288021844893564, "specificity": 0.9130434782608695, "sensitivity": 0.7647058823529411, "balanced_accuracy": 0.8388746803069054, "interpretability_score": 0.9}, "SVM": {"accuracy": 0.8, "precision": 0.8461538461538461, "recall": 0.6470588235294118, "f1_score": 0.7333333333333333, "mcc": 0.5911561035156879, "kappa": 0.5778364116094987, "auc_roc": 0.9207161125319694, "auc_pr": 0.9082446119142531, "specificity": 0.9130434782608695, "sensitivity": 0.6470588235294118, "balanced_accuracy": 0.7800511508951407, "interpretability_score": 0.4}, "KNN": {"accuracy": 0.875, "precision": 0.875, "recall": 0.8235294117647058, "f1_score": 0.8484848484848485, "mcc": 0.7432561251138006, "kappa": 0.7422680412371134, "auc_roc": 0.9322250639386189, "auc_pr": 0.9188618925831202, "specificity": 0.9130434782608695, "sensitivity": 0.8235294117647058, "balanced_accuracy": 0.8682864450127876, "interpretability_score": 0.6}, "NaiveBayes": {"accuracy": 0.875, "precision": 0.9285714285714286, "recall": 0.7647058823529411, "f1_score": 0.8387096774193549, "mcc": 0.7474980048088188, "kappa": 0.7382198952879582, "auc_roc": 0.89769820971867, "auc_pr": 0.9095747389865037, "specificity": 0.9565217391304348, "sensitivity": 0.7647058823529411, "balanced_accuracy": 0.860613810741688, "interpretability_score": 0.7}, "NeuralNet": {"accuracy": 0.875, "precision": 0.875, "recall": 0.8235294117647058, "f1_score": 0.8484848484848485, "mcc": 0.7432561251138006, "kappa": 0.7422680412371134, "auc_roc": 0.959079283887468, "auc_pr": 0.945048204390765, "specificity": 0.9130434782608695, "sensitivity": 0.8235294117647058, "balanced_accuracy": 0.8682864450127876, "interpretability_score": 0.3}}, "selection_reasoning": "推荐 Random Forest 作为最佳模型的理由：\n\n1. 在可解释性优先策略下，该模型获得了最高的综合得分 0.8752\n2. 可解释性评分: 0.80，模型决策过程相对透明\n3. 在保持可解释性的同时，F1分数达到 0.8750\n\n4. 该模型在二分类任务中展现出了优秀的综合性能"}